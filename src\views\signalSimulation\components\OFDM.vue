<script setup>
  import { plotToNum } from '@/utils/utils'
  import UnitCom from '@/components/UnitCom/index.vue'
  import useDictStore from '@/store/modules/dict'
  import { modulationTypeOptions } from '@/constant/simulation.js'

  const dictStore = useDictStore()
  const activeName = ref('SubCarrier') //默认展示调制参数
  const filterTypeOptions = reactive([])
  const dataSourceOptions = reactive([])

  // 调制参数
  const generalParaForm = ref({
    sequenceLength: 1000,
    prefixLength: 100,
    subCarrierSpacing: 100e3,
    overSamplingRatio: 1,
    windowEnable: false,
    windowLength: 10
  })

  // 数据源
  const subCarrierForm = ref({})

  // 滤波器
  const filterForm = ref({})

  const reset = () => {
    generalParaForm.value = {
      sequenceLength: 1000,
      prefixLength: 100,
      subCarrierSpacing: 100e3,
      overSamplingRatio: 1,
      windowEnable: false,
      windowLength: 10
    }
    subCarrierForm.value = {}
    filterForm.value = {}
  }

  const updateValue = (key, form, newValue, unit) => {
    form[key] = plotToNum(newValue + unit) // 更新表单中的原始值
  }
  const handleClick = (tab, event) => {
    console.log(tab, event)
  }

  const handleChange = newActiveName => {
    console.log(newActiveName)
  }

  const setFormData = data => {}

  const getDictionaryData = async () => {
    dictStore.dict
      .filter(item => item.dictType === 'digital_mod_filter')
      .forEach(item => {
        filterTypeOptions.push({ label: item.dictLabel, value: item.dictValue })
      })

    dictStore.dict
      .filter(item => item.dictType === 'digital_mod_data_source')
      .forEach(item => {
        dataSourceOptions.push({ label: item.dictLabel, value: item.dictValue })
      })
  }

  onMounted(() => {
    getDictionaryData()
  })

  defineExpose({
    generalParaForm,
    subCarrierForm,
    filterForm,
    reset,
    setFormData
  })

  const handleChangeModulationType = val => {
    console.log(val)
  }
</script>

<template>
  <el-tabs
    v-model="activeName"
    type="border-card"
    @tab-click="handleClick"
    @tab-change="handleChange"
    tab-position="top"
  >
    <el-tab-pane label="通用参数" name="GP">
      <el-form :model="generalParaForm" label-width="110" label-position="left">
        <el-form-item label="序列长度：">
          <el-input-number style="width: 200px" v-model="generalParaForm.sequenceLength">
            <template #suffix>符号</template>
          </el-input-number>
        </el-form-item>
        <el-form-item label="循环前缀长度：">
          <el-input-number style="width: 200px" v-model="generalParaForm.prefixLength">
            <template #suffix>符号</template>
          </el-input-number>
        </el-form-item>
        <el-form-item label="子载波间隔：">
          <UnitCom
            :value="generalParaForm.subCarrierSpacing"
            @update:value="updateValue('subCarrierSpacing', generalParaForm, $event, 'Hz')"
          />
        </el-form-item>
        <el-form-item label="过采样倍数：">
          <el-input-number
            style="width: 200px"
            type="number"
            v-model="generalParaForm.overSamplingRatio"
            :min="1"
            :max="16"
          >
            <template #suffix>倍</template>
          </el-input-number>
        </el-form-item>
        <el-form-item label="">
          <el-checkbox v-model="generalParaForm.windowEnable">加窗</el-checkbox>
        </el-form-item>
        <el-form-item v-if="generalParaForm.windowEnable" label="窗长度：">
          <el-input-number style="width: 200px" v-model="generalParaForm.windowLength">
            <template #suffix>符号</template>
          </el-input-number>
        </el-form-item>
      </el-form>
    </el-tab-pane>

    <el-tab-pane label="子载波分配" name="SubCarrier">
      <el-form :model="subCarrierForm" label-width="140" label-position="left">
        <el-form-item label="子载波总数：">
          <el-input-number
            style="width: 200px"
            :min="4"
            :max="4096"
            :step="1"
            v-model="subCarrierForm.subCarrierNum"
          >
          </el-input-number>
        </el-form-item>
        <el-form-item label="占用子载波数：">
          <el-input-number
            style="width: 200px"
            :min="4"
            :max="4096"
            :step="1"
            v-model="subCarrierForm.occupiedSubCarrierNum"
          >
          </el-input-number>
        </el-form-item>
        <el-form-item label="插入空DC子载波：">
          <el-checkbox v-model="subCarrierForm.insertEmptyDCSubCarrier"></el-checkbox>
        </el-form-item>
        <el-form-item label="左侧保护子载波数：">
          <el-input-number
            style="width: 200px"
            :min="0"
            :max="2047"
            :step="1"
            v-model="subCarrierForm.leftGuardSubCarrierNum"
          >
          </el-input-number>
        </el-form-item>
        <el-form-item label="右侧保护子载波数：">
          <el-input-number style="width: 200px" v-model="subCarrierForm.rightGuardSubCarrierNum">
          </el-input-number>
        </el-form-item>
        <el-form-item label="先导输入：">
          <el-checkbox v-model="subCarrierForm.leaderInput"></el-checkbox>
        </el-form-item>
        <el-form-item v-if="subCarrierForm.leaderInput" label="先导子载波数：">
          <el-input-number
            style="width: 200px"
            :min="0"
            :max="2047"
            :step="1"
            disabled
            v-model="subCarrierForm.leaderSubCarrierNum"
          >
          </el-input-number>
        </el-form-item>
        <el-form-item v-if="subCarrierForm.leaderInput" label="先导子载波索引：">
          <el-input-number style="width: 200px" v-model="subCarrierForm.leaderSubCarrierIndex">
          </el-input-number>
        </el-form-item>
        <el-form-item v-if="subCarrierForm.leaderInput" label="先导位数据源：">
          <el-select style="width: 200px" v-model="subCarrierForm.leaderDataSource">
            <el-option
              v-for="item in dataSourceOptions"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="subCarrierForm.leaderInput" label="先导调制样式：">
          <el-cascader
            v-model="subCarrierForm.leaderModulationType"
            :options="modulationTypeOptions"
            :show-all-levels="false"
          />
        </el-form-item>
      </el-form>
    </el-tab-pane>

    <!-- <el-tab-pane label="滤波器" name="Filters">
      <el-form :model="filterForm" label-width="90" label-position="left">
        <el-form-item label="滤波器：">
          <el-select
            style="width: 200px"
            v-model="filterForm.filterType"
            placeholder="请选择滤波器"
          >
            <el-option
              v-for="item in filterTypeOptions"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Alpha/BT：">
          <el-input type="number" style="width: 200px" v-model="filterForm.phaseModOffset">
          </el-input>
        </el-form-item>
      </el-form>
    </el-tab-pane> -->
  </el-tabs>
</template>

<style scoped lang="scss">
  :deep(.cusBtn) {
    width: 50%;
  }
  :deep(.el-radio-button--large .el-radio-button__inner) {
    width: 100%;
  }

  :deep(.el-tabs__nav) {
    float: none !important;
  }
  :deep(.el-tabs__nav .is-top) {
    width: 25% !important;
  }
</style>
