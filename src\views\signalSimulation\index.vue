<script setup>
  import UnitCom from '@/components/UnitCom/index.vue'
  import AnalogModulation from './components/AnalogModulation.vue'
  import DigitalModulation from './components/DigitalModulation.vue'
  import { plotToNum } from '@/utils/utils.js'

  const activeName = ref('DigitalModulation')
  const loading = ref(false)
  const DigitalModulationComRef = ref(null)
  const AnalogModulationComRef = ref(null)

  const simulationForm = ref({
    carrierFrequency: 200e3, // 载波频率
    signalPower: 10.0 // 信号功率
  })

  const deviceTestInfo = ref([
    {
      deviceAddress: '设备地址：*************',
      devicePort: '设备端口：7777',
      workStatus: '工作状态：未连接',
      faultDescription: ''
    }
  ])

  // 计算属性：判断是否有记录包含 faultDescription（非空字符串）
  const hasFaultDescription = computed(() => {
    return deviceTestInfo.value.some(
      item => item.faultDescription && item.faultDescription.trim() !== ''
    )
  })

  // 根据是否存在 faultDescription 决定表格的背景色
  const tableBgClass = computed(() => {
    return hasFaultDescription.value ? 'row-fault' : 'row-noFault'
  })

  const updateValue = (key, form, newValue, unit) => {
    form[key] = plotToNum(newValue + unit) // 更新表单中的原始值
  }

  const handleClick = (tab, event) => {
    console.log(tab, event)
  }

  /** 复位 */
  const handleReset = () => {
    simulationForm.value = {
      carrierFrequency: 200e3, // 载波频率
      signalPower: 10.0 // 信号功率
    }
  }

  /** 加载 */
  const handleLoad = () => {
    console.log(simulationForm.value)
  }

  /**保存 */
  const handleSave = () => {}

  const handleSignalGraph = () => {
    console.log(activeName.value)
  }

  onMounted(() => {})

  onBeforeUnmount(() => {})
</script>

<template>
  <div class="relative w-full h-full">
    <cu-title title="信号模拟"></cu-title>
    <el-form>
      <el-form-item label="载波频率：" prop="carrierFrequency">
        <UnitCom
          :value="simulationForm.carrierFrequency"
          @update:value="
            (newValue, unit) => updateValue('carrierFrequency', simulationForm, newValue, unit)
          "
        />
      </el-form-item>
      <el-form-item label="信号功率：" prop="signalPower">
        <el-space>
          <el-input-number
            style="width: 200px"
            v-model="simulationForm.signalPower"
            :min="3"
            :max="50"
            :precision="2"
            :step="0.01"
            placeholder="请输入信号功率"
          />
          <span>dBm</span>
        </el-space>
      </el-form-item>
      <el-form-item label="信号设置：">
        <cu-button content="复位" @click="handleReset"></cu-button>
        <cu-button content="加载" @click="handleLoad"></cu-button>
        <cu-button content="保存" @click="handleSave"></cu-button>
      </el-form-item>
    </el-form>
    <div class="flex gap-8" v-loading="loading">
      <el-tabs
        class="w-11/12 min-h-[350px]"
        v-model="activeName"
        type="border-card"
        @tab-click="handleClick"
        tab-position="left"
      >
        <el-tab-pane label="数字调制" name="DigitalModulation">
          <DigitalModulation ref="DigitalModulationComRef" />
        </el-tab-pane>
        <el-tab-pane label="模拟调制" name="AnalogModulation">
          <AnalogModulation ref="AnalogModulationComRef" />
        </el-tab-pane>
        <el-tab-pane label="雷达信号" name="RadarSignal">
          <!-- <NoiseCom ref="NoiseComRef" /> -->
        </el-tab-pane>
        <el-tab-pane label="任意波" name="ArbitraryWave">
          <!-- <NoiseCom ref="NoiseComRef" /> -->
        </el-tab-pane>
        <el-tab-pane label="OFDM" name="OFDM">
          <!-- <NoiseCom ref="OFDM" /> -->
        </el-tab-pane>
      </el-tabs>
      <div class="w-1/12 flex items-center justify-center">
        <cu-button content="信号图" @click="handleSignalGraph"></cu-button>
      </div>
    </div>

    <!-- 设备信息 -->
    <div class="absolute -bottom-4 -right-4">
      <vxe-table
        class="max-w-[1000px]"
        :class="tableBgClass"
        :show-header="false"
        :data="deviceTestInfo"
        border
      >
        <vxe-column field="deviceAddress" title="设备地址" width="160" align="center" />
        <vxe-column field="devicePort" title="设备端口" width="100" align="center" />
        <vxe-column field="workStatus" title="工作状态" width="100" align="center" />
        <!-- 如果存在 faultDescription，则显示该列 -->
        <vxe-column
          v-if="hasFaultDescription"
          field="faultDescription"
          title="故障描述"
          align="center"
        />
      </vxe-table>
    </div>
  </div>
</template>

<style scoped lang="scss">
  :deep(.vxe-table--body) {
    color: #fff !important;
  }
  :deep(.row-noFault .vxe-table--body) {
    background-color: #56bcbe !important ;
  }
  :deep(.row-fault .vxe-table--body) {
    background-color: #e89e42 !important ;
  }
  :deep(.funBtn .vxe-button + .vxe-button) {
    margin-left: 0 !important;
  }
</style>
