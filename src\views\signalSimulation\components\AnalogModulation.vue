<script setup>
  import { plotToNum } from '@/utils/utils'
  import UnitCom from '@/components/UnitCom/index.vue'
  import useDictStore from '@/store/modules/dict'

  const dictStore = useDictStore()
  const activeName = ref('AM') //默认展示幅度调制
  const standardWaveformOptions = reactive([])
  const pilmTrainingOptions = reactive([])

  // 幅度调制
  const amplitudeModulationForm = ref({
    waveformType: '正弦波', //标准波形
    modRate: 200e3, //调制率
    modDeepValue: 50 //调制深度
  })

  // 频率调制
  const freqModulationForm = ref({
    waveformType: '正弦波', //标准波形
    freq: 20e3, //频率
    deviation: 500e3 // 偏差
  })

  // 相位调制
  const phaseModulationForm = ref({
    waveformType: '正弦波', //标准波形
    freq: 20e3, //频率
    phaseModOffset: 2.4 //调相相偏
  })

  // 脉冲调制
  const pulseModulationForm = ref({
    pulseWidth: 5, // 脉宽 (us)
    repetitionFreq: 10, // 重周 (us)
    delayTime: 10, // 时延 (us)
    mode: 'Single', // 模式
    pulseNum: 0, // 脉冲数
    pulsePeriod: 0 // 脉冲周期 (us)
  })

  const reset = () => {
    amplitudeModulationForm.value = {
      waveformType: '正弦波', //标准波形
      modRate: 200e3, //调制率
      modDeepValue: 50 //调制深度
    }
    freqModulationForm.value = {
      waveformType: '正弦波', //标准波形
      freq: 20e3, //频率
      deviation: 500e3 // 偏差
    }
    phaseModulationForm.value = {
      waveformType: '正弦波', //标准波形
      freq: 20e3, //频率
      phaseModOffset: 2.4 //调相相偏
    }
    pulseModulationForm.value = {
      pulseWidth: 5, // 脉宽 (us)
      repetitionFreq: 10, // 重周 (us)
      delayTime: 10, // 时延 (us)
      mode: 'Single', // 模式
      pulseNum: 0, // 脉冲数
      pulsePeriod: 0 // 脉冲周期 (us)
    }
  }

  const updateValue = (key, form, newValue, unit) => {
    form[key] = plotToNum(newValue + unit) // 更新表单中的原始值
  }
  const handleClick = (tab, event) => {
    console.log(tab, event)
  }

  const handleChange = newActiveName => {
    console.log(newActiveName)
    reset()
  }

  const setFormData = data => {
    amplitudeModulationForm.value = { ...data.amplitudeModulation }
    freqModulationForm.value = { ...data.freqModulation }
    phaseModulationForm.value = { ...data.phaseModulation }
    if (data.amplitudeModulation?.enable) {
      activeName.value = 'AM'
    } else if (data.freqModulation?.enable) {
      activeName.value = 'FM'
    } else if (data.phaseModulation?.enable) {
      activeName.value = 'PM'
    } else {
      activeName.value = 'AM'
    }
  }

  const getDictionaryData = async () => {
    dictStore.dict
      .filter(item => item.dictType === 'waveform_type')
      .slice(0, -1) // 去除最后一项
      .forEach(item => {
        standardWaveformOptions.push({ label: item.dictLabel, value: item.dictValue })
      })
    dictStore.dict
      .filter(item => item.dictType === 'pilm_train_mode')
      .forEach(item => {
        pilmTrainingOptions.push({ label: item.dictLabel, value: item.dictValue })
      })
  }

  onMounted(() => {
    getDictionaryData()
  })

  defineExpose({
    amplitudeModulationForm,
    freqModulationForm,
    phaseModulationForm,
    pulseModulationForm,
    reset,
    setFormData
  })
</script>

<template>
  <el-tabs
    v-model="activeName"
    type="border-card"
    @tab-click="handleClick"
    @tab-change="handleChange"
    tab-position="top"
  >
    <el-tab-pane label="幅度调制" name="AM">
      <el-form :model="amplitudeModulationForm" label-width="90" label-position="left">
        <el-form-item label="标准波形：">
          <el-select
            style="width: 200px"
            v-model="amplitudeModulationForm.waveformType"
            placeholder="请选择波形"
          >
            <el-option
              v-for="item in standardWaveformOptions"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="调制频率：">
          <UnitCom
            :value="amplitudeModulationForm.modRate"
            @update:value="
              (newValue, unit) => updateValue('modRate', amplitudeModulationForm, newValue, unit)
            "
          />
        </el-form-item>
        <el-form-item label="调制深度：">
          <el-input
            type="number"
            style="width: 200px"
            v-model="amplitudeModulationForm.modDeepValue"
          >
            <template #append> % </template>
          </el-input>
        </el-form-item>
      </el-form>
    </el-tab-pane>

    <el-tab-pane label="频率调制" name="FM">
      <el-form :model="freqModulationForm" label-width="90" label-position="left">
        <el-form-item label="标准波形：">
          <el-select
            style="width: 200px"
            v-model="freqModulationForm.waveformType"
            placeholder="请选择波形"
          >
            <el-option
              v-for="item in standardWaveformOptions"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="频率：">
          <UnitCom
            :value="freqModulationForm.freq"
            @update:value="
              (newValue, unit) => updateValue('freq', freqModulationForm, newValue, unit)
            "
          />
        </el-form-item>
        <el-form-item label="偏差：">
          <UnitCom
            :value="freqModulationForm.deviation"
            :unit-options="['kHz', 'MHz']"
            @update:value="
              (newValue, unit) => updateValue('deviation', freqModulationForm, newValue, unit)
            "
          />
        </el-form-item>
      </el-form>
    </el-tab-pane>

    <el-tab-pane label="相位调制" name="PM">
      <el-form :model="phaseModulationForm" label-width="90" label-position="left">
        <el-form-item label="标准波形：">
          <el-select
            style="width: 200px"
            v-model="phaseModulationForm.waveformType"
            placeholder="请选择波形"
          >
            <el-option
              v-for="item in standardWaveformOptions"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="频率：">
          <UnitCom
            :value="phaseModulationForm.freq"
            @update:value="
              (newValue, unit) => updateValue('freq', phaseModulationForm, newValue, unit)
            "
          />
        </el-form-item>
        <el-form-item label="调相相偏：">
          <el-input type="number" style="width: 200px" v-model="phaseModulationForm.phaseModOffset">
            <template #append> rad </template>
          </el-input>
        </el-form-item>
      </el-form>
    </el-tab-pane>

    <el-tab-pane label="脉冲调制" name="PULM">
      <el-form :model="pulseModulationForm" label-width="90" label-position="left">
        <el-form-item label="脉宽：">
          <el-input type="number" style="width: 200px" v-model="pulseModulationForm.pulseWidth">
            <template #append> us </template>
          </el-input>
        </el-form-item>
        <el-form-item label="重周：">
          <el-input type="number" style="width: 200px" v-model="pulseModulationForm.repetitionFreq">
            <template #append> us </template>
          </el-input>
        </el-form-item>
        <el-form-item label="时延：">
          <el-input type="number" style="width: 200px" v-model="pulseModulationForm.delayTime">
            <template #append> us </template>
          </el-input>
        </el-form-item>
        <el-form-item label="模式：">
          <el-select
            style="width: 200px"
            v-model="pulseModulationForm.mode"
            placeholder="请选择模式"
          >
            <el-option
              v-for="item in pilmTrainingOptions"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="pulseModulationForm.mode === 'Pulse-train'" label="脉冲数：">
          <el-input type="number" style="width: 200px" v-model="pulseModulationForm.pulseNum">
            <template #append> us </template>
          </el-input>
        </el-form-item>
        <el-form-item v-if="pulseModulationForm.mode === 'Pulse-train'" label="脉冲周期：">
          <el-input type="number" style="width: 200px" v-model="pulseModulationForm.pulsePeriod">
            <template #append> us </template>
          </el-input>
        </el-form-item>
      </el-form>
    </el-tab-pane>
  </el-tabs>
</template>

<style scoped lang="scss">
  :deep(.cusBtn) {
    width: 50%;
  }
  :deep(.el-radio-button--large .el-radio-button__inner) {
    width: 100%;
  }

  :deep(.el-tabs__nav) {
    float: none !important;
  }
  :deep(.el-tabs__nav .is-top) {
    width: 33.3% !important;
  }
</style>
