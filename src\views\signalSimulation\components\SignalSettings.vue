<script setup>
  import { plotToNum } from '@/utils/utils'
  import UnitCom from '@/components/UnitCom/index.vue'
  import useDictStore from '@/store/modules/dict'

  const dictStore = useDictStore()
  const activeName = ref('RealTimeBaseBand') // 默认激活实时基带

  //实时基带
  const realTimeBaseBandForm = ref({
    enable: false,
    dataOrigin: 0, //数据源
    bitRate: 200e3, //码速率
    modType: '', //调制样式
    freqOffset: 200, //频偏
    phasePolarity: 0, //相位极性
    differentialEncod: 0, //差分编码
    filterType: '', //滤波器类型
    filterOrde: 0.3 //滤波器阶数
  })

  //双音
  const dualToneForm = ref({
    enable: false,
    amplitudeModType: 0, //调幅类型
    freqSpacing: 200e3 //频率间隔
  })

  // 多音
  const multiToneForm = ref({
    enable: false,
    audioNum: 2, //多音个数
    freqSpacing: 200e3, //频率间隔
    initPhase: 0, //初始相位
    tonePhase: 0, //音间相位
    modDeep: 7 //调制深度
  })

  //数据源枚举
  const dataSourceOptions = [
    { label: 'fileStream', value: 0, text: '文件码流' },
    { label: '0', value: 1, text: '全0' },
    { label: '1', value: 2, text: '全1' },
    { label: 'FRBS', value: 3, text: 'FRBS' },
    { label: 'custom', value: 4, text: '自定义序列' }
  ]
  const modulationModeOptions = reactive([]) //调制样式
  const filterOptions = [{ label: '根奈奎斯特滤波器', value: '0' }] //滤波器

  // 重置表单
  const reset = () => {
    realTimeBaseBandForm.value = {
      enable: false,
      dataOrigin: 0, //数据源
      bitRate: 200e3, //码速率
      modType: '', //调制样式
      freqOffset: 200, //频偏
      phasePolarity: 0, //相位极性
      differentialEncod: 0, //差分编码
      filterType: '', //滤波器类型
      filterOrde: 0.3 //滤波器阶数
    }
    dualToneForm.value = {
      enable: false,
      amplitudeModType: 0, //调幅类型
      freqSpacing: 200e3 //频率间隔
    }
    multiToneForm.value = {
      enable: false,
      audioNum: 2, //多音个数
      freqSpacing: 200e3, //频率间隔
      initPhase: 0, //初始相位
      tonePhase: 0, //音间相位
      modDeep: 7 //调制深度
    }
  }

  const updateValue = (key, form, newValue, unit) => {
    console.log(key, newValue, unit)
    form[key] = plotToNum(newValue + unit) // 更新表单中的原始值
    console.log(form[key])
  }
  const handleClick = (tab, event) => {
    console.log(tab, event)
  }

  const handleChange = newActiveName => {
    console.log(newActiveName)
    reset()
  }

  /**
   * @description: 选择文件
   */
  const selectFile = () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.onchange = () => {
      realTimeBaseBandForm.value.fileName = input.files[0].name
    }
    input.click()
  }

  const setFormData = data => {
    realTimeBaseBandForm.value = { ...data.realTimeBaseBand }
    dualToneForm.value = { ...data.dualTone }
    multiToneForm.value = { ...data.multiTone }
    if (data.realTimeBaseBand?.enable) {
      activeName.value = 'RealTimeBaseBand'
    } else if (data.dualTone?.enable) {
      activeName.value = 'DualTone'
    } else if (data.multiTone?.enable) {
      activeName.value = 'Polyphony'
    }
  }

  const getDictionaryData = async () => {
    dictStore.dict
      .filter(item => item.dictType === 'modulation_type')
      .forEach(item => {
        modulationModeOptions.push({ label: item.dictLabel, value: item.dictValue })
      })
  }

  onMounted(() => {
    getDictionaryData()
  })

  defineExpose({
    realTimeBaseBandForm,
    dualToneForm,
    multiToneForm,
    reset,
    setFormData
  })
</script>

<template>
  <el-tabs
    v-model="activeName"
    type="border-card"
    @tab-click="handleClick"
    @tab-change="handleChange"
    tab-position="left"
  >
    <el-tab-pane label="实时基带" name="RealTimeBaseBand">
      <el-form :model="realTimeBaseBandForm" label-width="auto" label-position="left">
        <el-form-item label="">
          <el-checkbox v-model="realTimeBaseBandForm.enable">启用</el-checkbox>
        </el-form-item>
        <el-form-item style="align-items: flex-start" label="数据源：">
          <div class="flex flex-col">
            <!-- 数据源单选框 -->
            <el-radio-group
              v-model="realTimeBaseBandForm.dataOrigin"
              size="large"
              style="width: 480px"
              class="data-source-group"
            >
              <el-radio-button
                v-for="item in dataSourceOptions"
                :key="item.label"
                :label="item.label"
                :value="item.value"
                class="cusBtnDataSource"
              >
                {{ item.text }}
              </el-radio-button>
            </el-radio-group>

            <!-- 文件选择输入框 -->
            <div class="flex items-center mt-4">
              <el-input v-model="realTimeBaseBandForm.fileName" style="width: 400px"></el-input>
              <cu-button content="浏览" class="ml-4" @click="selectFile" />
            </div>
          </div>
        </el-form-item>
        <el-form-item label="码速率：">
          <UnitCom
            :value="realTimeBaseBandForm.bitRate"
            :unit-options="['kbps', 'Mbps']"
            @update:value="
              (newValue, unit) => updateValue('bitRate', realTimeBaseBandForm, newValue, unit)
            "
          />
        </el-form-item>
        <el-form-item label="调制样式：">
          <el-select
            style="width: 200px; margin-right: 20px"
            v-model="realTimeBaseBandForm.modType"
          >
            <el-option
              v-for="item in modulationModeOptions"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            />
          </el-select>
          <div class="flex items-center">
            <span class="mr-2 w-[50px]">频偏</span>
            <el-input style="width: 200px" v-model="realTimeBaseBandForm.freqOffset">
              <template #append> kHz </template>
            </el-input>
          </div>
        </el-form-item>
        <el-form-item label="相位极性：">
          <el-radio-group
            v-model="realTimeBaseBandForm.phasePolarity"
            size="large"
            style="width: 200px"
          >
            <el-radio-button class="cusBtn" label="normal" :value="0">正常</el-radio-button>
            <el-radio-button class="cusBtn" label="revert" :value="1">翻转</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="差分编码：">
          <el-radio-group
            v-model="realTimeBaseBandForm.differentialEncod"
            size="large"
            style="width: 200px"
          >
            <el-radio-button class="cusBtn" label="on" :value="0">开</el-radio-button>
            <el-radio-button class="cusBtn" label="off" :value="1">关</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="滤波器：">
          <el-select
            style="width: 200px; margin-right: 20px"
            v-model="realTimeBaseBandForm.filterType"
          >
            <el-option
              v-for="item in filterOptions"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            />
          </el-select>
          <div class="flex items-center">
            <span class="mr-2">α</span>
            <el-input v-model="realTimeBaseBandForm.filterOrde"> </el-input>
          </div>
        </el-form-item>
      </el-form>
    </el-tab-pane>
    <el-tab-pane label="双音" name="DualTone">
      <el-form :model="dualToneForm" label-width="auto" label-position="left">
        <el-form-item label="">
          <el-checkbox v-model="dualToneForm.enable">启用</el-checkbox>
        </el-form-item>
        <el-form-item label="调幅类型：">
          <el-radio-group v-model="dualToneForm.amplitudeModType" size="large" style="width: 200px">
            <el-radio-button class="cusBtn" label="linear" :value="0">线性</el-radio-button>
            <el-radio-button class="cusBtn" label="index" :value="1">指数</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="频率间隔：">
          <UnitCom
            :value="dualToneForm.freqSpacing"
            @update:value="
              (newValue, unit) => updateValue('freqSpacing', dualToneForm, newValue, unit)
            "
          />
        </el-form-item>
      </el-form>
    </el-tab-pane>
    <el-tab-pane label="多音" name="Polyphony">
      <el-form :model="multiToneForm" label-width="auto" label-position="left">
        <el-form-item label="">
          <el-checkbox v-model="multiToneForm.enable">启用</el-checkbox>
        </el-form-item>

        <el-form-item label="音频个数：">
          <el-input style="width: 200px" v-model="multiToneForm.audioNum"> </el-input>
        </el-form-item>
        <el-form-item label="频率间隔：">
          <UnitCom
            :value="multiToneForm.freqSpacing"
            @update:value="
              (newValue, unit) => updateValue('freqSpacing', multiToneForm, newValue, unit)
            "
          />
        </el-form-item>
        <el-form-item label="初始相位：">
          <el-radio-group v-model="multiToneForm.initPhase" size="large" style="width: 200px">
            <el-radio-button class="cusBtn" label="fixed" :value="0">固定</el-radio-button>
            <el-radio-button class="cusBtn" label="random" :value="1">随机</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="音间相位：">
          <el-radio-group v-model="multiToneForm.tonePhase" size="large" style="width: 200px">
            <el-radio-button class="cusBtn" label="fixed" :value="0">固定</el-radio-button>
            <el-radio-button class="cusBtn" label="random" :value="1">随机</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="调制深度：">
          <el-input style="width: 200px" v-model="multiToneForm.modDeep">
            <template #append> deg </template>
          </el-input>
        </el-form-item>
      </el-form>
    </el-tab-pane>
  </el-tabs>
</template>

<style scoped lang="scss">
  :deep(.cusBtnDataSource) {
    width: 20%;
  }
  :deep(.cusBtn) {
    width: 50%;
  }
  :deep(.el-radio-button--large .el-radio-button__inner) {
    width: 100%;
  }
</style>
