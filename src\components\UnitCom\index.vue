<script setup>
  import { ref, watch } from 'vue'

  const props = defineProps({
    // 父组件传入的“真实值”（默认单位 Hz/bps等）
    value: {
      type: Number,
      default: 200e3
    },
    // 用户可选择的单位选项
    unitOptions: {
      type: Array,
      default: () => ['kHz', 'MHz', 'GHz']
    }
  })

  const emit = defineEmits(['update:value'])

  const firstDisplay = ref(false)
  // 单位映射表：将“真实值(Hz)”转换到当前单位
  const unitsMap = {
    Hz: 1,
    kHz: 1e3,
    MHz: 1e6,
    GHz: 1e9,
    bps: 1,
    kbps: 1e3,
    Mbps: 1e6,
    Gbps: 1e9
  }

  // 当前选择的单位，默认为 unitOptions[0]（若不在映射表里，则取第一个可用的）
  const unit = ref(
    props.unitOptions[0] in unitsMap ? props.unitOptions[0] : Object.keys(unitsMap)[0]
  )

  // 用于显示的值，字符串形式，便于用户输入
  const displayValue = ref(0)

  // 根据父组件传入的真实值，转换为当前 unit 下的“显示值”
  function toDisplayValue(realVal, unitLabel) {
    const factor = unitsMap[unitLabel] || 1
    return (realVal / factor).toString()
  }

  // 当父组件的 value 改变时，重新计算 displayValue（不再做单位自动切换）
  watch(
    () => props.value,
    newVal => {
      console.log(firstDisplay.value)
      if (!firstDisplay.value) {
        if (newVal >= 1e9 && newVal % 1e9 === 0) {
          unit.value = props.unitOptions[0].includes('Hz')
            ? 'GHz'
            : props.unitOptions.length > 2
            ? 'Gbps'
            : 'Mbps'
        } else if (newVal >= 1e6 && newVal % 1e6 === 0) {
          unit.value = props.unitOptions[0].includes('Hz') ? 'MHz' : 'Mbps'
        } else if (newVal >= 1e3 && newVal % 1e3 === 0) {
          unit.value = props.unitOptions[0].includes('Hz') ? 'kHz' : 'kbps'
        }
        firstDisplay.value = true
      }
      displayValue.value = toDisplayValue(newVal, unit.value)
    },
    { immediate: true }
  )

  // 用户修改单位 => 重新计算显示值 & 发出事件
  function onUnitChange(newUnit) {
    unit.value = newUnit
    // displayValue.value = toDisplayValue(props.value, newUnit)
    // 告知父组件，“当前显示值”和“当前单位”是什么
    emit('update:value', displayValue.value, newUnit)
  }

  // 用户修改输入框 => 直接发出事件，告诉父组件“显示值”和“单位”
  function onInputChange() {
    if (displayValue.value === '') return
    emit('update:value', displayValue.value, unit.value)
  }

  onBeforeMount(() => {
    firstDisplay.value = false
  })
</script>

<template>
  <!-- 输入框，用于显示“换算后的值” -->
  <el-input style="width: 200px" v-model="displayValue" @input="onInputChange" />
  <!-- 单位选择 -->
  <el-radio-group v-model="unit" size="large" class="ml-5" @change="onUnitChange">
    <el-radio-button v-for="item in unitOptions" :key="item" :label="item" :value="item" />
  </el-radio-group>
</template>
